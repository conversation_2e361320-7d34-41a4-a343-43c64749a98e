# app/handlers/image_generation.py
import asyncio
import logging
from typing import Optional

from aiogram import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Router
from aiogram.filters import Command
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.types import BufferedInputFile, CallbackQuery, Message

import app.keyboards as kb
from app.localization import get_text
from app.services.image_generation.manager import get_generator
from app.supabase.users import get_user_image_model, get_user_language
from app.subscription_manager import SubscriptionManager
from app.text_messages import llm_response
from app.utils.error_handler import handle_error
from app.utils.request_limiter import check_limits
from config.image_generation_config import IMAGE_MODELS_CONFIG
from app.mcp_client.manager import MCPClientManager

logger = logging.getLogger(__name__)
router = Router()

GENERATING_USERS = set()

class ImageGeneration(StatesGroup):
    waiting_for_prompt = State()
    waiting_for_size = State()

async def enhance_prompt(prompt: str, user_id: int, mcp_manager: Optional[MCPClientManager] = None) -> tuple[str, bool]:
    """
    Улучшает описание изображения с помощью LLM.
    Возвращает кортеж: (улучшенный_промпт, флаг_нарушения_модерации).
    """
    system_msg = "Enhance this image description adding details about lighting, colors, and composition. English only, max 40 words."
    try:
        response = await llm_response(
            f"{system_msg}\n\nInput: {prompt}", user_id, mcp_manager, use_mcp_tools=False
        )
        if isinstance(response, dict) and "error" in response:
            if "системой модерации контента" in response["error"]:
                logger.warning(f"Запрос на улучшение промпта отклонен модератором: {prompt}")
                return prompt, True
            return prompt, False

        if response and hasattr(response, 'choices'):
            return response.choices[0].message.content, False
    except Exception as e:
        logger.error(f"Ошибка улучшения промпта: {e}")
    return prompt, False

@router.message(Command("image"))
async def start_image_generation(message: Message, state: FSMContext):
    user_id = message.from_user.id
    if user_id in GENERATING_USERS:
        user_language = await get_user_language(user_id)
        await message.answer(get_text("image_generation", "already_generating", user_language))
        return

    can_proceed, _ = await check_limits(message, "image")
    if not can_proceed:
        return

    user_language = await get_user_language(user_id)
    await message.answer(
        get_text("image_generation", "size_selection", user_language),
        reply_markup=kb.image_size_selection,
    )
    await state.set_state(ImageGeneration.waiting_for_size)

@router.callback_query(ImageGeneration.waiting_for_size, lambda c: c.data and c.data.startswith('size_'))
async def process_size_selection(callback_query: CallbackQuery, state: FSMContext):
    size = callback_query.data.split('_')[1]
    await state.update_data(image_size=size)
    user_language = await get_user_language(callback_query.from_user.id)
    await callback_query.message.edit_text(
        text=get_text("image_generation", "prompt_request", user_language),
        reply_markup=None,
    )
    await state.set_state(ImageGeneration.waiting_for_prompt)
    await callback_query.answer()


@router.message(ImageGeneration.waiting_for_prompt, ~F.text.startswith("/"))
async def generate_image_from_prompt(message: Message, state: FSMContext, bot: Bot, dispatcher: Dispatcher):
    user_id = message.from_user.id
    if user_id in GENERATING_USERS:
        user_language = await get_user_language(user_id)
        await message.answer(get_text("image_generation", "already_generating", user_language))
        return

    state_data = await state.get_data()
    image_size = state_data.get('image_size', '1:1')
    prompt = message.text
    await state.clear()

    user_language = await get_user_language(user_id)
    processing_message = await message.answer(get_text("image_generation", "processing", user_language))

    GENERATING_USERS.add(user_id)

    try:
        mcp_manager = dispatcher.get('mcp_manager')
        final_prompt, moderation_violation = await enhance_prompt(prompt, user_id, mcp_manager)

        user_model_id = await get_user_image_model(user_id)
        generator = get_generator(user_model_id)

        image_bytes = await generator.generate(final_prompt, image_size)

        if image_bytes:
            photo = BufferedInputFile(image_bytes, filename="generated_image.png")
            model_name = IMAGE_MODELS_CONFIG[user_model_id]['name']

            await processing_message.delete()
            await message.answer_photo(
                photo=photo,
                caption=get_text("image_generation", "result_caption", user_language).format(
                    prompt=prompt,
                    used_prompt=final_prompt,
                    size=image_size,
                    model_name=model_name,
                ),
            )
            if moderation_violation:
                await message.answer(get_text("image_generation", "content_filter_warning", user_language), parse_mode="Markdown")

            await SubscriptionManager.increment_request_counter(user_id, "image")
        else:
            await processing_message.edit_text(get_text("image_generation", "generation_failed", user_language))

    except Exception as e:
        await handle_error(e, message, "errors.image_generation_error", additional_info="Ошибка в хендлере generate_image_from_prompt")
        if processing_message:
            await processing_message.delete()
    finally:
        GENERATING_USERS.discard(user_id)
