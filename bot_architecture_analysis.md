# Архитектура Telegram-бота: Полный анализ для интеграции редактирования изображений

## 1. Общая архитектура бота

### Язык и фреймворки
- **Язык**: Python 3.13
- **Telegram фреймворк**: aiogram 3.x
- **Асинхронность**: asyncio/aiohttp
- **LLM интеграция**: OpenAI API (через LiteLLM)

### Управление состоянием (State Management)
**Текущее состояние**: Бот НЕ использует FSM (Finite State Machine) или централизованное управление состоянием.

**Критическая информация для редактирования изображений**:
- Отсутствует система хранения "контекста активного изображения"
- Каждое сообщение обрабатывается независимо
- Состояние пользователя хранится только в базе данных (настройки модели, язык)

**Рекомендация**: Для редактирования изображений потребуется:
- Внедрить FSM для отслеживания активного изображения
- Временное хранение изображений (Redis/файловая система)
- Контекст операций редактирования

### База данных
**Система**: Supabase (PostgreSQL)

**Основные таблицы**:
```sql
-- Пользователи и их настройки
users:
  - user_id (bigint, primary key)
  - username (text)
  - first_name (text)
  - last_name (text)
  - language_code (text, default: 'ru')
  - model_id (text, default: 'gpt-4.1')
  - image_model_id (text, default: 'flux')
  - created_at (timestamp)
  - updated_at (timestamp)
```

**Файлы работы с БД**:
- `app/supabase/client.py` - клиент Supabase
- `app/supabase/users.py` - операции с пользователями

### Фоновые задачи
**Текущее состояние**: Отсутствуют
- Нет очередей задач (Celery, Dramatiq)
- Все операции выполняются синхронно в контексте обработчика

**Для редактирования изображений может потребоваться**:
- Фоновая обработка тяжелых операций
- Очередь для batch-операций

### Деплоймент
**Конфигурация**:
- Переменные окружения в `.env` файле
- Конфигурационные файлы в директории `config/`
- Логирование через Python logging

## 2. Механизм вызова инструментов (Tool Calling)

### Основная LLM
**Модели** (из `config/models_config.py`):
```python
MODELS_CONFIG = {
    "gpt-4.1": {
        "name": "GPT-4.1",
        "tools": True,  # Поддержка инструментов
        "vision": True,
        "base_url": "http://**************:4000/v1"
    },
    "gemini-2.5-flash-preview-05-20": {
        "name": "Gemini 2.5 Pro", 
        "tools": True,
        "vision": True
    }
}
```

### Формат определения инструмента
**Протокол Tool** (`app/tools/base_tool.py`):
```python
class Tool(Protocol):
    name: str  # Уникальное имя для OpenAI API
    description: str  # Описание для LLM
    schema: Dict[str, Any]  # JSON Schema параметров
    
    async def execute(self, bot: Bot, chat_id: int, **kwargs) -> str:
        """Выполнение инструмента"""
        ...
```

**Пример существующего инструмента** (`app/tools/implementations/image_generator_tool.py`):
```python
class ImageGeneratorTool:
    name: str = "generate_image"
    description: str = "Generates an image based on a user's text description and desired aspect ratio. Use this when the user asks to draw, create, or generate a picture."
    schema: dict = {
        "type": "object",
        "properties": {
            "prompt": {
                "type": "string",
                "description": "A detailed and vivid text description of the image to be generated. IMPORTANT: Always write the description in English, regardless of the user's language."
            },
            "size": {
                "type": "string",
                "description": "The aspect ratio of the image.",
                "enum": ["1:1", "16:9", "9:16", "7:4"],
                "default": "1:1"
            }
        },
        "required": ["prompt"]
    }
    
    async def execute(self, bot: Bot, chat_id: int, prompt: str, size: str = '1:1') -> str:
        # Логика генерации изображения
        user_image_model = await get_user_image_model(chat_id)
        generator = get_generator(user_image_model)
        image_bytes = await generator.generate(prompt, size)
        
        # Отправка изображения пользователю
        photo = BufferedInputFile(image_bytes, filename="generated_image.png")
        await bot.send_photo(chat_id=chat_id, photo=photo, caption=caption)
        
        return "Image was successfully generated and sent to the user."
```

### Жизненный цикл вызова инструментов

**1. Регистрация инструментов** (`config/tools_config.py`):
```python
TOOLS_CONFIG = {
    "image_generation": {
        "module": "app.tools.implementations.image_generator_tool",
        "class": "ImageGeneratorTool", 
        "enabled": True,
        "description": "Генерация изображений по текстовому описанию"
    }
}
```

**2. Загрузка и формирование схем** (`app/tools/registry.py`):
```python
class ToolRegistry:  # Singleton
    async def load_tools(self):
        # Динамический импорт enabled инструментов
        for tool_name, tool_config in TOOLS_CONFIG.items():
            if tool_config.get('enabled', False):
                module = importlib.import_module(tool_config['module'])
                tool_class = getattr(module, tool_config['class'])
                tool_instance = tool_class()
                self._tools[tool_instance.name] = tool_instance
    
    def get_openai_tools_schema(self):
        # Формирование схем для OpenAI API
        return [{
            "type": "function",
            "function": {
                "name": tool.name,
                "description": tool.description,
                "parameters": tool.schema
            }
        } for tool in self._tools.values()]
```

**3. Основной цикл обработки** (`app/text_messages.py`):
```python
async def llm_response(message_text, user_id, bot, message, use_tool_calling=True):
    # 1. Получение инструментов
    registry = ToolRegistry()
    await registry.load_tools()
    tool_calling_tools = registry.get_openai_tools_schema()
    
    # 2. Отправка запроса в LLM
    response = await client.chat.completions.create(
        model=model_id,
        messages=messages,
        tools=tool_calling_tools,  # Передача инструментов
        tool_choice="auto"
    )
    
    # 3. Обработка tool_calls
    if response.choices[0].message.tool_calls:
        tool_results = []
        for tool_call in response.choices[0].message.tool_calls:
            # Выполнение инструмента
            result = await call_tool_calling_tool(tool_call, bot, message)
            tool_results.append(result)
        
        # 4. Отправка результатов обратно в LLM
        messages.append(response.choices[0].message)
        messages.extend(tool_results)
        
        # Финальный ответ от LLM
        final_response = await client.chat.completions.create(
            model=model_id,
            messages=messages
        )
```

**4. Выполнение конкретного инструмента**:
```python
async def call_tool_calling_tool(tool_call, bot, message):
    tool_name = tool_call.function.name
    arguments = json.loads(tool_call.function.arguments)
    
    registry = ToolRegistry()
    result = await registry.execute_tool(tool_name, arguments, bot, message.chat_id)
    
    return {
        "role": "tool",
        "tool_call_id": tool_call.id,
        "content": result
    }
```

### Обработка ошибок
**Многоуровневая система**:
1. **Уровень инструмента**: try/catch в execute()
2. **Уровень реестра**: валидация существования инструмента
3. **Уровень вызова**: обработка JSON parsing ошибок
4. **Возврат в LLM**: ошибки передаются как tool result

```python
# Пример обработки ошибки
try:
    result = await tool.execute(bot, chat_id, **arguments)
except Exception as e:
    error_msg = f"Ошибка выполнения инструмента {name}: {e}"
    logger.error(error_msg)
    return json.dumps({"error": error_msg})

## 3. Структура проекта

```
GPT_Project/
├── main.py                          # Точка входа, запуск бота
├── requirements.txt                 # Зависимости Python
├── .env                            # Переменные окружения
├── .gitignore                      # Git исключения
│
├── app/                            # Основной код приложения
│   ├── __init__.py
│   ├── bot.py                      # Инициализация aiogram бота
│   ├── text_messages.py            # ⭐ КЛЮЧЕВОЙ ФАЙЛ: LLM интеграция и tool calling
│   ├── localization.py             # Система локализации
│   │
│   ├── handlers/                   # Обработчики Telegram событий
│   │   ├── __init__.py
│   │   ├── common.py               # Общие команды (/start, /help)
│   │   ├── image_generation.py     # Обработка генерации изображений
│   │   ├── model_selection.py      # Выбор LLM модели
│   │   ├── language_selection.py   # Выбор языка интерфейса
│   │   └── role_selection.py       # Выбор роли/промпта
│   │
│   ├── tools/                      # ⭐ СИСТЕМА TOOL CALLING
│   │   ├── __init__.py
│   │   ├── base_tool.py            # Протокол Tool
│   │   ├── registry.py             # ToolRegistry (Singleton менеджер)
│   │   └── implementations/        # Реализации инструментов
│   │       ├── __init__.py
│   │       └── image_generator_tool.py  # Единственный активный инструмент
│   │
│   ├── services/                   # Бизнес-логика и внешние сервисы
│   │   ├── __init__.py
│   │   └── image_generation/       # ⭐ МОДУЛЬНАЯ СИСТЕМА ГЕНЕРАЦИИ ИЗОБРАЖЕНИЙ
│   │       ├── __init__.py
│   │       ├── base.py             # Протокол ImageGenerator
│   │       ├── manager.py          # Фабрика генераторов (с кешированием)
│   │       └── providers/          # Провайдеры генерации
│   │           ├── __init__.py
│   │           ├── pollinations.py # Pollinations.ai API
│   │           └── openai.py       # OpenAI DALL-E API
│   │
│   ├── supabase/                   # Интеграция с Supabase (PostgreSQL)
│   │   ├── __init__.py
│   │   ├── client.py               # Клиент Supabase
│   │   └── users.py                # Операции с пользователями
│   │
│   └── mcp_client/                 # MCP (Model Context Protocol) клиент
│       ├── __init__.py
│       └── manager.py              # Менеджер MCP серверов
│
├── config/                         # ⭐ КОНФИГУРАЦИОННЫЕ ФАЙЛЫ
│   ├── __init__.py
│   ├── models_config.py            # Конфигурация LLM моделей
│   ├── tools_config.py             # ⭐ Конфигурация Tool Calling инструментов
│   ├── image_generation_config.py  # Конфигурация генерации изображений
│   ├── mcp_servers.json           # Конфигурация MCP серверов
│   └── roles/                     # Системные промпты для ролей
│       ├── assistant.txt
│       ├── translator.txt
│       └── creative_writer.txt
│
├── localization/                   # Файлы локализации
│   ├── en.json                    # Английский
│   ├── ru.json                    # Русский
│   └── uk.json                    # Украинский
│
└── logs/                          # Логи приложения (создается автоматически)
```

### Ключевые файлы для интеграции редактирования изображений

#### 1. `app/text_messages.py` - Центральный координатор
**Функции**:
- `llm_response()` - основная функция обработки сообщений
- `call_tool_calling_tool()` - выполнение Tool Calling инструментов
- `call_mcp_tool()` - выполнение MCP инструментов
- `get_mcp_tools_for_openai()` - получение MCP инструментов

#### 2. `app/tools/` - Система инструментов
**Архитектура**:
- `base_tool.py` - Protocol для всех инструментов
- `registry.py` - Singleton менеджер с динамической загрузкой
- `implementations/` - конкретные реализации

#### 3. `config/tools_config.py` - Конфигурация инструментов
```python
TOOLS_CONFIG = {
    "image_generation": {
        "module": "app.tools.implementations.image_generator_tool",
        "class": "ImageGeneratorTool",
        "enabled": True,
        "description": "Генерация изображений по текстовому описанию"
    }
    # Здесь будет добавлен image_editing инструмент
}
```

#### 4. `app/services/image_generation/` - Модульная система обработки изображений
**Компоненты**:
- `base.py` - Protocol ImageGenerator
- `manager.py` - Фабрика с кешированием
- `providers/` - различные API провайдеры

## 4. Критические точки для интеграции редактирования изображений

### 4.1 Управление состоянием
**Проблема**: Отсутствует система хранения контекста активного изображения

**Решение**: Потребуется добавить:
```python
# Новый модуль app/state/image_context.py
class ImageEditingContext:
    user_id: int
    original_image_path: str
    current_image_path: str
    edit_history: List[EditOperation]
    created_at: datetime
```

### 4.2 Хранение изображений
**Текущее состояние**: Изображения не сохраняются, только генерируются и отправляются

**Потребуется**:
- Временное хранение загруженных изображений
- Система очистки старых файлов
- Возможно интеграция с облачным хранилищем

### 4.3 Новый инструмент редактирования
**Структура**:
```python
# app/tools/implementations/image_editor_tool.py
class ImageEditorTool:
    name: str = "edit_image"
    description: str = "Edits an uploaded image based on user instructions"
    schema: dict = {
        "type": "object",
        "properties": {
            "operation": {
                "type": "string",
                "enum": ["resize", "crop", "filter", "adjust_brightness", "adjust_contrast"]
            },
            "parameters": {
                "type": "object",
                "description": "Operation-specific parameters"
            }
        },
        "required": ["operation"]
    }
```

### 4.4 Обработчики изображений
**Потребуется новый файл**: `app/handlers/image_editing.py`
- Обработка загруженных изображений
- Инициация контекста редактирования
- Интеграция с FSM

## 5. Рекомендации по архитектуре

### 5.1 Добавить FSM для контекста
```python
# app/states/image_editing.py
from aiogram.fsm.state import State, StatesGroup

class ImageEditingStates(StatesGroup):
    waiting_for_image = State()
    image_uploaded = State()
    editing_in_progress = State()
```

### 5.2 Расширить систему провайдеров
```python
# app/services/image_processing/
├── base.py              # Protocol ImageProcessor
├── manager.py           # Фабрика процессоров
└── providers/
    ├── pillow_processor.py    # Базовая обработка через Pillow
    ├── opencv_processor.py    # Продвинутая обработка через OpenCV
    └── ai_processor.py        # AI-редактирование через внешние API
```

### 5.3 Система кеширования
- Redis для хранения контекста сессий
- Файловая система для временных изображений
- Автоматическая очистка по TTL

Этот документ предоставляет полную картину архитектуры для успешной интеграции функции редактирования изображений.
```
