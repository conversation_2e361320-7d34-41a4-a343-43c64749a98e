import os
import string
import random
import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from app.supabase.client import supabase_client
from config.models_config import DEFAULT_MODEL, MODELS_CONFIG
from config.roles_config import ROLES_CONFIG, DEFAULT_ROLE_ID
from config.image_generation_config import DEFAULT_IMAGE_MODEL, IMAGE_MODELS_CONFIG
from app.cache.user_cache import UserCache, cached_user

# Настройка логирования
logger = logging.getLogger(__name__)

def generate_referral_code(length=8) -> str:
    """
    Генерирует уникальный реферальный код.

    Args:
        length: Длина реферального кода

    Returns:
        str: Сгенерированный реферальный код
    """
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))

@cached_user
async def get_user_by_tg_id(tg_id: int) -> Optional[Dict[str, Any]]:
    """
    Получает пользователя по Telegram ID.
    Использует кеширование для оптимизации запросов.

    Args:
        tg_id: ID пользователя в Telegram

    Returns:
        Optional[Dict[str, Any]]: Данные пользователя или None, если пользователь не найден
    """
    try:
        response = await asyncio.to_thread(
            supabase_client.table("users").select("*").eq("tg_id", tg_id).execute
        )

        if response.data and len(response.data) > 0:
            return response.data[0]
        return None
    except Exception as e:
        logger.error(f"Ошибка при получении пользователя по tg_id {tg_id}: {str(e)}")
        return None

async def set_user(tg_id: int, referral_code: Optional[str] = None, language: str = "ru") -> str:
    """
    Создает нового пользователя или возвращает существующего.

    Args:
        tg_id: ID пользователя в Telegram
        referral_code: Реферальный код пригласившего пользователя
        language: Код языка интерфейса (ru, en)

    Returns:
        str: Реферальный код пользователя
    """
    try:
        # Проверяем, существует ли пользователь
        user = await get_user_by_tg_id(tg_id)

        if not user:
            # Генерируем новый реферальный код
            new_referral_code = generate_referral_code()

            # Создаем данные нового пользователя
            new_user_data = {
                "tg_id": tg_id,
                "model": DEFAULT_MODEL,
                "image_model": DEFAULT_IMAGE_MODEL,
                "role": DEFAULT_ROLE_ID,
                "referral_code": new_referral_code,
                "referral_count": 0,
                "subscription_plan": "free",
                "text_requests_today": 0,
                "image_requests_today": 0,
                "last_request_date": datetime.now().isoformat(),
                "language": language
            }

            # Если указан реферальный код, находим пригласившего пользователя
            if referral_code:
                referrer_response = await asyncio.to_thread(
                    supabase_client.table("users").select("id").eq("referral_code", referral_code).execute
                )

                if referrer_response.data and len(referrer_response.data) > 0:
                    referrer_id = referrer_response.data[0]["id"]
                    new_user_data["referrer_id"] = referrer_id

                    # Увеличиваем счетчик рефералов у пригласившего пользователя
                    await asyncio.to_thread(
                        supabase_client.table("users").update({"referral_count": referrer_response.data[0].get("referral_count", 0) + 1}).eq("id", referrer_id).execute
                    )

                    # Инвалидируем кеш для пригласившего пользователя
                    cache = UserCache()
                    referrer_tg_id = await get_tg_id_by_user_id(referrer_id)
                    if referrer_tg_id:
                        cache.invalidate(referrer_tg_id)

            # Создаем нового пользователя
            response = await asyncio.to_thread(
                supabase_client.table("users").insert(new_user_data).execute
            )

            if response.data and len(response.data) > 0:
                # Сохраняем нового пользователя в кеш
                cache = UserCache()
                cache.set(tg_id, response.data[0])
                return new_user_data["referral_code"]
            else:
                logger.error(f"Ошибка при создании пользователя с tg_id {tg_id}")
                return generate_referral_code()  # Возвращаем временный код в случае ошибки

        # Возвращаем реферальный код существующего пользователя
        return user["referral_code"]
    except Exception as e:
        logger.error(f"Ошибка при создании/получении пользователя с tg_id {tg_id}: {str(e)}")
        return generate_referral_code()  # Возвращаем временный код в случае ошибки

async def get_users() -> List[Dict[str, Any]]:
    """
    Получает список всех пользователей.

    Returns:
        List[Dict[str, Any]]: Список пользователей
    """
    try:
        response = await asyncio.to_thread(
            supabase_client.table("users").select("*").execute
        )
        return response.data if response.data else []
    except Exception as e:
        logger.error(f"Ошибка при получении списка пользователей: {str(e)}")
        return []

async def get_tg_id_by_user_id(user_id: int) -> Optional[int]:
    """
    Получает Telegram ID пользователя по его ID в базе данных.

    Args:
        user_id: ID пользователя в базе данных

    Returns:
        Optional[int]: Telegram ID пользователя или None, если пользователь не найден
    """
    try:
        response = await asyncio.to_thread(
            supabase_client.table("users").select("tg_id").eq("id", user_id).execute
        )

        if response.data and len(response.data) > 0:
            return response.data[0]["tg_id"]
        return None
    except Exception as e:
        logger.error(f"Ошибка при получении tg_id пользователя по ID {user_id}: {str(e)}")
        return None

async def set_user_model(tg_id: int, model: str) -> None:
    """
    Устанавливает модель для пользователя.

    Args:
        tg_id: ID пользователя в Telegram
        model: Название модели
    """
    try:
        user = await get_user_by_tg_id(tg_id)

        if user:
            response = await asyncio.to_thread(
                supabase_client.table("users").update({"model": model}).eq("id", user["id"]).execute
            )
            # Инвалидируем кеш для этого пользователя
            cache = UserCache()
            cache.invalidate(tg_id)
        else:
            # Если пользователь не найден, создаем нового
            new_user_data = {
                "tg_id": tg_id,
                "model": model,
                "image_model": DEFAULT_IMAGE_MODEL,
                "role": DEFAULT_ROLE_ID,
                "referral_code": generate_referral_code(),
                "referral_count": 0,
                "subscription_plan": "free",
                "text_requests_today": 0,
                "image_requests_today": 0,
                "last_request_date": datetime.now().isoformat()
            }
            response = await asyncio.to_thread(
                supabase_client.table("users").insert(new_user_data).execute
            )
            # Сохраняем нового пользователя в кеш
            if response.data and len(response.data) > 0:
                cache = UserCache()
                cache.set(tg_id, response.data[0])
    except Exception as e:
        logger.error(f"Ошибка при установке модели для пользователя с tg_id {tg_id}: {str(e)}")

async def get_user_model(tg_id: int) -> str:
    """
    Получает модель пользователя.

    Args:
        tg_id: ID пользователя в Telegram

    Returns:
        str: Название модели
    """
    try:
        user = await get_user_by_tg_id(tg_id)

        if user:
            current_model = user["model"]
            # Проверяем, существует ли модель в конфигурации
            if current_model not in MODELS_CONFIG:
                # Если модель не существует, обновляем на DEFAULT_MODEL
                current_model = DEFAULT_MODEL
                await set_user_model(tg_id, DEFAULT_MODEL)
            return current_model
        return DEFAULT_MODEL
    except Exception as e:
        logger.error(f"Ошибка при получении модели пользователя с tg_id {tg_id}: {str(e)}")
        return DEFAULT_MODEL

async def get_user_image_model(tg_id: int) -> str:
    """
    Получает модель изображений пользователя.

    Args:
        tg_id: ID пользователя в Telegram

    Returns:
        str: Название модели изображений
    """
    try:
        user = await get_user_by_tg_id(tg_id)

        if user:
            current_image_model = user["image_model"]
            # Проверяем, существует ли модель в конфигурации
            if current_image_model not in IMAGE_MODELS_CONFIG:
                # Если модель не существует, обновляем на DEFAULT_IMAGE_MODEL
                current_image_model = DEFAULT_IMAGE_MODEL
                await set_user_image_model(tg_id, DEFAULT_IMAGE_MODEL)
            return current_image_model
        return DEFAULT_IMAGE_MODEL
    except Exception as e:
        logger.error(f"Ошибка при получении модели изображений пользователя с tg_id {tg_id}: {str(e)}")
        return DEFAULT_IMAGE_MODEL

async def set_user_image_model(tg_id: int, image_model: str) -> None:
    """
    Устанавливает модель изображений для пользователя.

    Args:
        tg_id: ID пользователя в Telegram
        image_model: Название модели изображений
    """
    try:
        user = await get_user_by_tg_id(tg_id)

        if user:
            response = await asyncio.to_thread(
                supabase_client.table("users").update({"image_model": image_model}).eq("id", user["id"]).execute
            )
            # Инвалидируем кеш для этого пользователя
            cache = UserCache()
            cache.invalidate(tg_id)
        else:
            # Если пользователь не найден, создаем нового
            new_user_data = {
                "tg_id": tg_id,
                "model": DEFAULT_MODEL,
                "image_model": image_model,
                "role": DEFAULT_ROLE_ID,
                "referral_code": generate_referral_code(),
                "referral_count": 0,
                "subscription_plan": "free",
                "text_requests_today": 0,
                "image_requests_today": 0,
                "last_request_date": datetime.now().isoformat()
            }
            response = await asyncio.to_thread(
                supabase_client.table("users").insert(new_user_data).execute
            )
            # Сохраняем нового пользователя в кеш
            if response.data and len(response.data) > 0:
                cache = UserCache()
                cache.set(tg_id, response.data[0])
    except Exception as e:
        logger.error(f"Ошибка при установке модели изображений для пользователя с tg_id {tg_id}: {str(e)}")

async def set_user_role(tg_id: int, role_id: str) -> None:
    """
    Устанавливает роль для пользователя.

    Args:
        tg_id: ID пользователя в Telegram
        role_id: ID роли пользователя (например, "default", "emily", "isaac")
    """
    try:
        # Проверяем, что role_id является валидным
        if role_id not in ROLES_CONFIG:
            logger.warning(f"Invalid role_id '{role_id}' provided for user {tg_id}. Using default.")
            role_id = DEFAULT_ROLE_ID

        user = await get_user_by_tg_id(tg_id)

        if user:
            await asyncio.to_thread(
                supabase_client.table("users").update({"role": role_id}).eq("id", user["id"]).execute
            )
            # Инвалидируем кеш для этого пользователя
            cache = UserCache()
            cache.invalidate(tg_id)
    except Exception as e:
        logger.error(f"Ошибка при установке роли для пользователя с tg_id {tg_id}: {str(e)}")

async def get_user_role(tg_id: int) -> str:
    """
    Получает роль пользователя.

    Args:
        tg_id: ID пользователя в Telegram

    Returns:
        str: ID роли пользователя (например, "default", "emily", "isaac")
    """
    try:
        user = await get_user_by_tg_id(tg_id)
        role_from_db = user["role"] if user else None

        # Проверяем, является ли значение валидным ID роли
        if role_from_db in ROLES_CONFIG:
            return role_from_db
        else:
            # Если в БД старое имя файла или невалидный ID, возвращаем дефолтный ID
            logger.warning(f"Invalid role '{role_from_db}' found for user {tg_id}. Returning default.")
            return DEFAULT_ROLE_ID
    except Exception as e:
        logger.error(f"Ошибка при получении роли пользователя с tg_id {tg_id}: {str(e)}")
        return DEFAULT_ROLE_ID

async def get_user_referral_count(tg_id: int) -> int:
    """
    Получает количество рефералов пользователя.

    Args:
        tg_id: ID пользователя в Telegram

    Returns:
        int: Количество рефералов
    """
    try:
        user = await get_user_by_tg_id(tg_id)
        return user["referral_count"] if user else 0
    except Exception as e:
        logger.error(f"Ошибка при получении количества рефералов пользователя с tg_id {tg_id}: {str(e)}")
        return 0

async def get_user_referral_code(tg_id: int) -> Optional[str]:
    """
    Получает реферальный код пользователя.

    Args:
        tg_id: ID пользователя в Telegram

    Returns:
        Optional[str]: Реферальный код пользователя
    """
    try:
        user = await get_user_by_tg_id(tg_id)
        return user["referral_code"] if user else None
    except Exception as e:
        logger.error(f"Ошибка при получении реферального кода пользователя с tg_id {tg_id}: {str(e)}")
        return None

async def update_user_subscription(user_id: int, subscription_plan: str, subscription_end_date: str) -> bool:
    """
    Обновляет подписку пользователя.

    Args:
        user_id: ID пользователя в базе данных
        subscription_plan: Тип подписки
        subscription_end_date: Дата окончания подписки в формате ISO или None для бесплатного тарифа

    Returns:
        bool: True, если обновление прошло успешно, иначе False
    """
    try:
        # Обновляем данные пользователя
        update_data = {
            "subscription_plan": subscription_plan,
            "subscription_end_date": subscription_end_date,
            "text_requests_today": 0,
            "image_requests_today": 0,
            "last_request_date": datetime.now().isoformat()
        }

        response = await asyncio.to_thread(
            supabase_client.table("users").update(update_data).eq("id", user_id).execute
        )

        if response.data and len(response.data) > 0:
            logger.info(f"Подписка пользователя с ID {user_id} успешно обновлена на {subscription_plan}")
            return True
        else:
            logger.error(f"Ошибка при обновлении подписки пользователя с ID {user_id}")
            return False
    except Exception as e:
        logger.error(f"Ошибка при обновлении подписки пользователя с ID {user_id}: {str(e)}")
        return False

async def update_user_limits(user_id: int, text_requests: int, image_requests: int, last_request_date: str) -> bool:
    """
    Обновляет лимиты запросов пользователя.

    Args:
        user_id: ID пользователя в базе данных
        text_requests: Количество текстовых запросов
        image_requests: Количество запросов на изображения
        last_request_date: Дата последнего запроса в формате ISO

    Returns:
        bool: True, если обновление прошло успешно, иначе False
    """
    try:
        # Обновляем данные пользователя
        update_data = {
            "text_requests_today": text_requests,
            "image_requests_today": image_requests,
            "last_request_date": last_request_date
        }

        response = await asyncio.to_thread(
            supabase_client.table("users").update(update_data).eq("id", user_id).execute
        )

        if response.data and len(response.data) > 0:
            logger.debug(f"Лимиты пользователя с ID {user_id} успешно обновлены")
            # Инвалидируем кеш для этого пользователя
            tg_id = await get_tg_id_by_user_id(user_id)
            if tg_id:
                cache = UserCache()
                cache.invalidate(tg_id)
            return True
        else:
            logger.error(f"Ошибка при обновлении лимитов пользователя с ID {user_id}")
            return False
    except Exception as e:
        logger.error(f"Ошибка при обновлении лимитов пользователя с ID {user_id}: {str(e)}")
        return False



async def get_stats() -> Dict[str, Any]:
    """
    Получает статистику по пользователям.

    Returns:
        Dict[str, Any]: Статистика
    """
    try:
        # Общее количество пользователей
        total_users_response = await asyncio.to_thread(
            supabase_client.table("users").select("count", count="exact").execute
        )
        total_users = total_users_response.count if hasattr(total_users_response, 'count') else 0

        # Количество пользователей по тарифам
        free_users_response = await asyncio.to_thread(
            supabase_client.table("users").select("count", count="exact").eq("subscription_plan", "free").execute
        )
        free_users = free_users_response.count if hasattr(free_users_response, 'count') else 0

        standard_users_response = await asyncio.to_thread(
            supabase_client.table("users").select("count", count="exact").eq("subscription_plan", "standard").execute
        )
        standard_users = standard_users_response.count if hasattr(standard_users_response, 'count') else 0

        premium_users_response = await asyncio.to_thread(
            supabase_client.table("users").select("count", count="exact").eq("subscription_plan", "premium").execute
        )
        premium_users = premium_users_response.count if hasattr(premium_users_response, 'count') else 0

        # Активные пользователи за последние 7 дней
        seven_days_ago = (datetime.now() - timedelta(days=7)).isoformat()
        active_users_response = await asyncio.to_thread(
            supabase_client.table("users").select("count", count="exact").gte("last_request_date", seven_days_ago).execute
        )
        active_users = active_users_response.count if hasattr(active_users_response, 'count') else 0

        # Общее количество запросов
        total_text_requests_response = await asyncio.to_thread(
            supabase_client.table("users").select("text_requests_today").execute
        )
        total_text_requests = sum(user.get("text_requests_today", 0) for user in total_text_requests_response.data) if total_text_requests_response.data else 0

        total_image_requests_response = await asyncio.to_thread(
            supabase_client.table("users").select("image_requests_today").execute
        )
        total_image_requests = sum(user.get("image_requests_today", 0) for user in total_image_requests_response.data) if total_image_requests_response.data else 0

        total_requests = total_text_requests + total_image_requests
        avg_requests = round(total_requests / total_users if total_users > 0 else 0, 2)

        return {
            "total_users": total_users,
            "free_users": free_users,
            "standard_users": standard_users,
            "premium_users": premium_users,
            "active_users": active_users,
            "total_requests": total_requests,
            "avg_requests": avg_requests,
            "total_text_requests": total_text_requests,
            "total_image_requests": total_image_requests
        }
    except Exception as e:
        logger.error(f"Ошибка при получении статистики: {str(e)}")
        return {
            "total_users": 0,
            "free_users": 0,
            "standard_users": 0,
            "premium_users": 0,
            "active_users": 0,
            "total_requests": 0,
            "avg_requests": 0,
            "total_text_requests": 0,
            "total_image_requests": 0
        }

async def reset_user_limits(tg_id: int) -> bool:
    """
    Сбрасывает лимиты запросов пользователя.

    Args:
        tg_id: ID пользователя в Telegram

    Returns:
        bool: True, если сброс прошел успешно, иначе False
    """
    try:
        user = await get_user_by_tg_id(tg_id)

        if not user:
            logger.error(f"Пользователь с tg_id {tg_id} не найден")
            return False

        # Обновляем данные пользователя
        update_data = {
            "text_requests_today": 0,
            "image_requests_today": 0,
            "last_request_date": datetime.now().isoformat()
        }

        response = await asyncio.to_thread(
            supabase_client.table("users").update(update_data).eq("id", user["id"]).execute
        )

        if response.data and len(response.data) > 0:
            logger.debug(f"Лимиты пользователя с tg_id {tg_id} успешно сброшены")
            return True
        else:
            logger.error(f"Ошибка при сбросе лимитов пользователя с tg_id {tg_id}")
            return False
    except Exception as e:
        logger.error(f"Ошибка при сбросе лимитов пользователя с tg_id {tg_id}: {str(e)}")
        return False

async def get_user_language(tg_id: int) -> str:
    """
    Получает язык интерфейса пользователя.

    Args:
        tg_id: ID пользователя в Telegram

    Returns:
        str: Код языка (ru, en). По умолчанию 'ru'
    """
    try:
        user = await get_user_by_tg_id(tg_id)

        if user and "language" in user:
            return user["language"]
        return "ru"  # Язык по умолчанию
    except Exception as e:
        logger.error(f"Ошибка при получении языка пользователя с tg_id {tg_id}: {str(e)}")
        return "ru"  # Язык по умолчанию в случае ошибки

async def set_user_language(tg_id: int, language: str) -> bool:
    """
    Устанавливает язык интерфейса пользователя.

    Args:
        tg_id: ID пользователя в Telegram
        language: Код языка (ru, en)

    Returns:
        bool: True, если язык успешно установлен, иначе False
    """
    try:
        user = await get_user_by_tg_id(tg_id)

        if not user:
            logger.error(f"Пользователь с tg_id {tg_id} не найден")
            return False

        # Проверяем, что язык поддерживается
        if language not in ["ru", "en"]:
            logger.warning(f"Язык {language} не поддерживается. Используется язык по умолчанию: ru")
            language = "ru"

        # Обновляем язык пользователя
        response = await asyncio.to_thread(
            supabase_client.table("users").update({"language": language}).eq("id", user["id"]).execute
        )

        if response.data and len(response.data) > 0:
            logger.debug(f"Язык пользователя с tg_id {tg_id} успешно установлен на {language}")
            # Инвалидируем кеш для этого пользователя
            cache = UserCache()
            cache.invalidate(tg_id)
            return True
        else:
            logger.error(f"Ошибка при установке языка пользователя с tg_id {tg_id}")
            return False
    except Exception as e:
        logger.error(f"Ошибка при установке языка пользователя с tg_id {tg_id}: {str(e)}")
        return False