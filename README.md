# Telegram AI Chatbot

Многофункциональный Telegram-бот с искусственным интеллектом, поддерживающий различные языковые модели, генерацию изображений и голосовое управление.

## 🌟 Возможности

- 🤖 **Языковые модели**:
  - GPT-4o
  - GPT-4o-mini
  - SearchGPT
  - Gemini 2.5 PRO
  - Поддержка дополнительных моделей через конфигурацию
- 🎨 **Генерация изображений**:
  - Различные соотношения сторон (1:1, 16:9, 9:16 и др.)
  - Улучшение промптов с помощью ИИ
- 👁️ **Компьютерное зрение (Vision)**:
  - Анализ изображений с помощью ИИ
  - Умное ограничение количества изображений в истории
- 🗣️ **Голосовое управление**:
  - Распознавание голосовых сообщений
  - Преобразование текста в речь
- 💎 **Система подписок**:
  - Бесплатный тариф (25 текстовых запросов, 10 запросов изображений в день)
  - Стандартный тариф (99 XTR, 500 текстовых запросов, 350 запросов изображений в день)
  - Премиум тариф (196 XTR, безлимитные запросы)
- 👥 **Реферальная система**:
  - Уникальные реферальные коды
  - Отслеживание приглашенных пользователей
- 📊 **Административные функции**:
  - Статистика использования
  - Рассылка сообщений
  - Управление лимитами пользователей

## 🔧 Технические требования

-   Python 3.8+
-   **Supabase (облачная база данных)**: Необходимо создать проект в Supabase и настроить базу данных.
-   Сервер litellm прокси
-   Telegram Bot API токен

## 🚀 Установка и настройка

### Для Linux/macOS:

1.  **Клонирование репозитория**:

    ```bash
    git clone https://github.com/Lorodn4x/GPT_Project.git
    cd GPT_Project
    ```

2.  **Создание виртуального окружения**:

    ```bash
    # Создаем виртуальное окружение Python
    python3 -m venv venv

    # Активация виртуального окружения
    source venv/bin/activate
    ```

3.  **Установка зависимостей**:

    ```bash
    pip install -r requirements.txt
    ```

### Для Windows:

1.  **Клонирование репозитория**:

    ```bash
    git clone https://github.com/Lorodn4x/GPT_Project.git
    cd GPT_Project
    ```

2.  **Создание виртуального окружения**:

    ```bash
    # Создаем виртуальное окружение Python
    python -m venv venv

    # Активация виртуального окружения
    venv\Scripts\activate
    ```

3.  **Установка зависимостей**:

    ```bash
    pip install -r requirements.txt
    ```

4.  **Настройка переменных окружения**:
    Создайте файл `.env` со следующими параметрами:

    ```
    TG_TOKEN=your_telegram_bot_token
    GROQ_API_KEY_70B=your_groq_api_key
    MISTRAL_API_KEY_MISTRAL_LARGE_LATEST=your_mistral_api_key
    DEFAULT_MODEL=llama-3.3-70b-versatile
    DEFAULT_PROMPT=Default
    ROLES="Эмили:Emily,Айзек:Lsaac,По умолчанию:Default"
    MAX_MESSAGES=30
    MAX_VISION_IMAGES=3
    ADMINS=your_telegram_id
    VOICE=ru-RU-SvetlanaNeural
    RATE=+0%
    PITCH=+0Hz
    VOLUME=+0%
    SUPABASE_URL=your_supabase_url
    SUPABASE_ANON_KEY=your_supabase_anon_key
    ```

5.  **Настройка логирования (опционально)**:
    Файл `run.py` настроен на ведение подробных логов. Логи сохраняются в папку `logs` с ротацией по дням.

6.  **Запуск бота**:

    ```bash
    python run.py
    ```

    При запуске, бот выполняет проверку подключения к базе данных Supabase. Убедитесь, что переменные окружения `SUPABASE_URL` и `SUPABASE_ANON_KEY` установлены корректно.

## 🎯 Команды бота

-   `/start` - Начало работы с ботом
-   `/newsletter` - Рассылка сообщений (только для администраторов)
-   `/stats` - Просмотр статистики бота (только для администраторов)
-   `/reset_limits <user_id>` - Сброс лимитов пользователя (только для администраторов). Вместо `<user_id>` подставьте Telegram ID пользователя.

## 💡 Основные функции интерфейса

-   **📬 Новый диалог** - Начать новую беседу
-   **👤 Мой профиль** - Управление настройками и подписками
-   **🎨 Генерация изображения** - Создание изображений по описанию
-   **🔗 Реферальная система** - Управление реферальной программой

## 📋 Тарифные планы

-   **Бесплатный**:
    -   25 текстовых запросов в день
    -   10 запросов на генерацию изображений в день

-   **Стандартный** (99 XTR):
    -   500 текстовых запросов в день
    -   350 запросов на генерацию изображений в день

-   **Премиум** (196 XTR):
    -   Безлимитные текстовые запросы
    -   Безлимитные запросы на генерацию изображений
