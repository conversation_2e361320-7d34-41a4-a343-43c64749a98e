# config/roles_config.py
from typing import TypedDict, Dict

class RoleInfo(TypedDict):
    name: str
    prompt_file: str
    # description: str # Пока не добавляем

ROLES_CONFIG: Dict[str, RoleInfo] = {
    "default": {
        "name": "По умолчанию",
        "prompt_file": "Default.txt",
    },
    "emily": {
        "name": "Эмил<PERSON>",
        "prompt_file": "Emily.txt",
    },
    "isaac": {
        "name": "Айзек",
        "prompt_file": "Lsaac.txt", # Оставляем старое имя файла Lsaac.txt
    },
    "tutor": {
        "name": "Решебник",
        "prompt_file": "Tutor.txt",
    },
    "programmer": {
        "name": "Программист",
        "prompt_file": "Programmer.txt",
    }
    # Другие роли будут добавлены позже централизованно здесь
}

DEFAULT_ROLE_ID = "default"
