import os
import speech_recognition as sr
import asyncio
import aiofiles
from typing import List, Awaitable, Callable
from pydub import AudioSegment
from aiogram.types import Message, BufferedInputFile
from aiogram.enums import ParseMode
from aiogram import Dispatcher
from app.text_messages import llm_response
from app.supabase.messages import save_message
from app.supabase.users import get_user_by_tg_id, update_user_limits, get_user_language
import tempfile
import logging
import aiohttp
import re
from app.subscription_manager import SubscriptionManager
from app.localization import get_text
from app.utils.request_limiter import check_limits
from app.utils.formatting import convert_markdown_to_html

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Загрузка переменных окружения
from dotenv import load_dotenv
load_dotenv()

MAX_VOICE_SIZE = 10 * 1024 * 1024  # 10 МБ
MAX_CONCURRENT_TTS_REQUESTS = 3  # Максимальное количество одновременных запросов к API

# Создаем семафор для ограничения количества одновременных запросов
tts_semaphore = asyncio.Semaphore(MAX_CONCURRENT_TTS_REQUESTS)

# Параметры API для озвучки текста
TTS_API_ENDPOINT = "https://www.openai.fm/api/generate"

# Максимальное количество символов в одном чанке для TTS API
TTS_CHUNK_LIMIT = 990  # Максимальное количество символов на один запрос

# Удаляем DEFAULT_VOICE и DEFAULT_VIBE_PROMPT, чтобы гарантировать использование переменных окружения

async def download_voice(message: Message, file_path: str) -> str:
    file_info = await message.bot.get_file(message.voice.file_id)
    await message.bot.download_file(file_info.file_path, file_path)
    return file_path

async def convert_ogg_to_wav(ogg_path: str, wav_path: str):
    def sync_convert():
        try:
            if not os.path.exists(ogg_path):
                raise FileNotFoundError(f"Исходный файл не найден: {ogg_path}")

            if os.path.getsize(ogg_path) == 0:
                raise ValueError("Исходный файл пуст")

            audio = AudioSegment.from_ogg(ogg_path)
            audio.export(wav_path, format="wav")

            if not os.path.exists(wav_path):
                raise RuntimeError("WAV файл не был создан")
        except FileNotFoundError as e:
            logger.error(f"Ошибка (sync): {e}")
            raise
        except ValueError as e:
            logger.error(f"Ошибка (sync): {e}")
            raise
        except Exception as e:
            logger.error(f"Неожиданная ошибка при конвертации (sync): {e}")
            raise

    try:
        await asyncio.to_thread(sync_convert)
    except Exception as e:
        logger.error(f"Ошибка выполнения convert_ogg_to_wav в потоке: {e}")
        raise

async def speech_to_text(wav_path: str) -> str:
    def sync_recognize():
        recognizer = sr.Recognizer()
        try:
            with sr.AudioFile(wav_path) as source:
                audio_data = recognizer.record(source)
            # Важно: recognize_google - это сетевая операция, она тоже блокирующая
            text = recognizer.recognize_google(audio_data, language="ru-RU")
            return text
        except sr.UnknownValueError:
            logger.error("Google Speech Recognition не смогла распознать аудио")
            raise
        except sr.RequestError as e:
            logger.error(f"Не удалось запросить результаты у Google Speech Recognition; {e}")
            raise
        except Exception as e:
            logger.error(f"Неожиданная ошибка при распознавании (sync): {e}")
            raise

    try:
        text = await asyncio.to_thread(sync_recognize)
        return text
    except Exception as e:
        logger.error(f"Ошибка выполнения speech_to_text в потоке: {e}")
        raise

async def split_text_into_chunks(text: str) -> list:
    """Разбивает текст на чанки, не превышающие TTS_CHUNK_LIMIT символов.
    Старается разбивать по границам предложений или абзацев."""
    # Если текст короче лимита, возвращаем его как единственный чанк
    if len(text) <= TTS_CHUNK_LIMIT:
        return [text]

    chunks = []
    current_position = 0

    while current_position < len(text):
        # Определяем конец текущего чанка (либо конец текста, либо лимит)
        chunk_end = min(current_position + TTS_CHUNK_LIMIT, len(text))

        # Если мы не достигли конца текста и текущий чанк достиг лимита,
        # ищем ближайшую границу предложения или абзаца для разбиения
        if chunk_end < len(text):
            # Ищем ближайший конец предложения (точка, восклицательный или вопросительный знак, за которым следует пробел или новая строка)
            sentence_end = max(
                text.rfind(". ", current_position, chunk_end),
                text.rfind("! ", current_position, chunk_end),
                text.rfind("? ", current_position, chunk_end),
                text.rfind(".\n", current_position, chunk_end),
                text.rfind("!\n", current_position, chunk_end),
                text.rfind("?\n", current_position, chunk_end)
            )

            # Ищем ближайший конец абзаца
            paragraph_end = text.rfind("\n\n", current_position, chunk_end)

            # Если нашли границу предложения или абзаца, используем её
            if sentence_end != -1 and (paragraph_end == -1 or sentence_end > paragraph_end):
                chunk_end = sentence_end + 1  # +1 чтобы включить точку
            elif paragraph_end != -1:
                chunk_end = paragraph_end + 2  # +2 чтобы включить оба символа новой строки
            else:
                # Если не нашли подходящей границы, ищем хотя бы пробел
                space = text.rfind(" ", current_position, chunk_end)
                if space != -1:
                    chunk_end = space + 1  # +1 чтобы включить пробел

        # Добавляем чанк в список
        chunks.append(text[current_position:chunk_end])
        current_position = chunk_end

    return chunks

async def synthesize_chunk(text: str, output_file: str, voice: str, vibe_prompt: str = None) -> bool:
    """Синтезирует один чанк текста в аудиофайл"""
    # Формируем параметры для запроса
    params = {
        'input': text,
        'voice': voice,
    }

    # Добавляем vibe_prompt только если он задан
    if vibe_prompt:
        params['prompt'] = vibe_prompt

    # Используем семафор для ограничения количества одновременных запросов
    async with tts_semaphore:
        try:
            async with aiohttp.ClientSession() as session:
                logger.info(f"Отправка запроса к TTS API для чанка длиной {len(text)} символов")
                async with session.get(TTS_API_ENDPOINT, params=params) as response:
                    if response.status == 200:
                        content = await response.read()
                        async with aiofiles.open(output_file, 'wb') as f:
                            await f.write(content)
                        logger.info(f"TTS API вернул успешный ответ для чанка")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"Ошибка API TTS: статус {response.status}, ответ: {error_text}")
                        return False
        except Exception as e:
            logger.error(f"Ошибка при обращении к API TTS для чанка: {e}")
            return False

async def merge_audio_files(chunk_files: list, output_file: str) -> bool:
    """Объединяет несколько аудиофайлов в один с помощью pydub"""
    def sync_merge():
        try:
            # Если есть только один файл, просто копируем его
            if len(chunk_files) == 1:
                with open(chunk_files[0], 'rb') as src, open(output_file, 'wb') as dst:
                    dst.write(src.read())
                return True

            # Объединяем несколько файлов
            combined = AudioSegment.empty()
            for file_path in chunk_files:
                if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
                    segment = AudioSegment.from_mp3(file_path)
                    combined += segment
                else:
                    logger.warning(f"Пропуск файла {file_path}: не существует или пуст")

            # Экспортируем результат
            combined.export(output_file, format="mp3")
            return True
        except Exception as e:
            logger.error(f"Ошибка при объединении аудиофайлов: {e}")
            return False

    try:
        return await asyncio.to_thread(sync_merge)
    except Exception as e:
        logger.error(f"Ошибка выполнения merge_audio_files в потоке: {e}")
        return False

async def text_to_speech(text: str, output_file: str):
    """Асинхронная функция для преобразования текста в речь с использованием API openai.fm
    Поддерживает длинные тексты путем разбиения на чанки и последующего объединения аудио."""
    # Получаем параметры из переменных окружения
    voice = os.getenv("TTS_VOICE", "")
    vibe_prompt = os.getenv("TTS_VIBE_PROMPT", "")

    # Если voice не задан, логируем предупреждение
    if not voice:
        logger.warning("TTS_VOICE не задан в переменных окружения, используется voice=alloy")
        voice = "alloy"

    # Логируем информацию о параметрах
    if vibe_prompt:
        logger.info(f"Используется TTS_VIBE_PROMPT из переменной окружения")
    else:
        logger.warning("TTS_VIBE_PROMPT не задан в переменных окружения")

    # Разбиваем текст на чанки
    chunks = await split_text_into_chunks(text)
    logger.info(f"Текст разбит на {len(chunks)} чанков")

    # Создаем временную директорию для хранения чанков
    temp_dir = tempfile.mkdtemp()
    chunk_files = []

    try:
        # Обрабатываем каждый чанк
        for i, chunk in enumerate(chunks):
            chunk_file = os.path.join(temp_dir, f"chunk_{i}.mp3")
            chunk_files.append(chunk_file)

            # Синтезируем речь для чанка
            success = await synthesize_chunk(chunk, chunk_file, voice, vibe_prompt)

            if not success:
                logger.error(f"Не удалось синтезировать чанк {i}")
                # Если хотя бы один чанк не удалось синтезировать, возвращаем False
                return False

        # Объединяем все чанки в один файл
        merge_success = await merge_audio_files(chunk_files, output_file)

        if merge_success:
            logger.info(f"Успешно объединены {len(chunk_files)} аудиофайлов в {output_file}")
            return True
        else:
            logger.error("Не удалось объединить аудиофайлы")
            return False

    except Exception as e:
        logger.error(f"Ошибка при обработке текста для TTS: {e}")
        return False
    finally:
        # Очищаем временные файлы
        for file_path in chunk_files:
            try:
                if os.path.exists(file_path):
                    await asyncio.to_thread(os.remove, file_path)
            except Exception as e:
                logger.error(f"Ошибка при удалении временного файла {file_path}: {e}")

        # Удаляем временную директорию
        try:
            if os.path.exists(temp_dir):
                await asyncio.to_thread(os.rmdir, temp_dir)
        except Exception as e:
            logger.error(f"Ошибка при удалении временной директории {temp_dir}: {e}")

async def handle_voice_message(message: Message, user_id: int, dispatcher: Dispatcher):
    # Проверяем лимиты и получаем пользователя
    can_proceed, user = await check_limits(message, "text")
    if not can_proceed:
        return

    processing_message = None
    try:
        if message.voice.file_size > MAX_VOICE_SIZE:
            await message.answer("Голосовое сообщение слишком большое. Максимальный размер: 10 МБ.")
            return

        # Отправляем сообщение о начале обработки из TEXTS
        user_language = await get_user_language(user_id)
        voice_processing_message = get_text("errors", "voice_processing", user_language)
        processing_message = await message.answer(voice_processing_message)

        with tempfile.TemporaryDirectory() as temp_dir:
            ogg_file_path = os.path.join(temp_dir, f"voice_{message.message_id}.ogg")
            wav_file_path = os.path.join(temp_dir, f"voice_{message.message_id}.wav")
            tts_output_file = os.path.join(temp_dir, f"response_{message.message_id}.mp3")

            # Скачивание голосового сообщения
            try:
                await download_voice(message, ogg_file_path)
            except Exception as e:
                logger.error(f"Ошибка при скачивании голосового сообщения: {e}")
                await message.answer("Ошибка при получении голосового сообщения. Попробуйте еще раз.")
                if processing_message:
                    await processing_message.delete()
                return

            # Конвертация OGG в WAV
            try:
                await convert_ogg_to_wav(ogg_file_path, wav_file_path)
            except FileNotFoundError:
                await message.answer("Ошибка: файл голосового сообщения не найден. Попробуйте отправить сообщение еще раз.")
                if processing_message:
                    await processing_message.delete()
                return
            except ValueError:
                await message.answer("Ошибка: полученный файл голосового сообщения пуст. Попробуйте записать новое сообщение.")
                if processing_message:
                    await processing_message.delete()
                return
            except Exception as e:
                logger.error(f"Ошибка при конвертации аудио: {e}")
                await message.answer(f"Ошибка при обработке аудио. Попробуйте еще раз или обратитесь в поддержку.")
                if processing_message:
                    await processing_message.delete()
                return

            # Преобразование речи в текст
            try:
                text = await speech_to_text(wav_file_path)
                formatted_message = f'голосовое сообщение: [{text}]'
            except Exception as e:
                logger.error(f"Ошибка при распознавании речи: {e}")
                await message.answer("Не удалось распознать речь. Попробуйте еще раз.")
                if processing_message:
                    await processing_message.delete()
                return

            # Сохранение сообщения в базу данных Supabase
            await save_message(role="user", content=formatted_message, user_id=user["id"])

            # Получение ответа от llm_response
            try:
                # Получаем mcp_manager из dispatcher
                mcp_manager_instance = dispatcher.get('mcp_manager')

                # Функция уведомления об использовании инструментов
                async def notify_tool_usage(tool_names: List[str]):
                    user_language = await get_user_language(user_id)
                    tool_usage_message = get_text("text_processing", "using_mcp_tools", user_language)
                    if processing_message: await processing_message.edit_text(tool_usage_message)

                response = await llm_response(
                    text,
                    user_id,
                    mcp_manager_instance,
                    bot=message.bot,
                    message=message,
                    tool_usage_callback=notify_tool_usage
                )
                if "error" in response:
                    if processing_message:
                        await processing_message.edit_text(response["error"])
                    return
                response_text = response.choices[0].message.content
                # Конвертируем Markdown в HTML
                html_response_text = convert_markdown_to_html(response_text)
            except Exception as e:
                logger.error(f"Ошибка при генерации ответа llm_response: {e}")
                await message.answer("Ошибка при генерации ответа. Попробуйте еще раз.")
                if processing_message:
                    await processing_message.delete()
                return

            # Сохранение ответа в базу данных Supabase
            await save_message(role="assistant", content=response_text, user_id=user["id"])

            # Новая реализация синтеза речи и отправки аудио
            await message.bot.send_chat_action(chat_id=message.chat.id, action="record_voice")
            tts_success = await text_to_speech(response_text, tts_output_file)

            if tts_success:
                try:
                    # Удаляем сообщение "Слушаю..." перед отправкой голосового ответа
                    if processing_message:
                        try:
                            await processing_message.delete()
                            processing_message = None # Обнуляем, чтобы не пытаться удалить снова
                        except Exception as e:
                            logger.error(f"Ошибка при удалении сообщения 'Слушаю...' перед отправкой голоса: {e}")

                    await message.bot.send_chat_action(chat_id=message.chat.id, action="upload_voice")
                    async with aiofiles.open(tts_output_file, "rb") as audio_file:
                        audio_content = await audio_file.read()
                        voice_response = BufferedInputFile(audio_content, filename="response.mp3")
                    await message.answer_voice(voice_response)
                except Exception as e:
                    logger.error(f"Ошибка при отправке голосового ответа: {e}")
                    try:
                        # Отправляем конвертированный HTML ответ
                        if processing_message: await processing_message.edit_text(html_response_text, parse_mode=ParseMode.HTML)
                    except Exception as e:
                        # Логируем и отправляем fallback
                        logger.error(f"Ошибка отправки HTML сообщения (Voice Fallback): {e}")
                        try:
                            # Fallback: отправляем оригинальный текст без форматирования
                            if processing_message: await processing_message.edit_text("Не удалось отформатировать ответ. Отправляю как есть:\n\n" + response_text, parse_mode=None)
                        except Exception as e2:
                            logger.error(f"Ошибка отправки простого текста (Voice Fallback): {e2}")
                            if processing_message: await processing_message.edit_text("Произошла ошибка при отправке ответа.")
            else:
                # Если синтез речи не удался, отправляем текстовый ответ
                try:
                    # Отправляем конвертированный HTML ответ
                    if processing_message: await processing_message.edit_text(html_response_text, parse_mode=ParseMode.HTML)
                except Exception as e:
                    # Логируем и отправляем fallback
                    logger.error(f"Ошибка отправки HTML сообщения (Voice Fallback): {e}")
                    try:
                        # Fallback: отправляем оригинальный текст без форматирования
                        if processing_message: await processing_message.edit_text("Не удалось отформатировать ответ. Отправляю как есть:\n\n" + response_text, parse_mode=None)
                    except Exception as e2:
                        logger.error(f"Ошибка отправки простого текста (Voice Fallback): {e2}")
                        if processing_message: await processing_message.edit_text("Произошла ошибка при отправке ответа.")

            # Сообщение об обработке уже удалено или заменено на ответ

        # Обновляем счетчик запросов в Supabase
        current_moscow_time = SubscriptionManager.get_moscow_time()

        user_data = await get_user_by_tg_id(user_id)
        if user_data:
            text_requests = user_data["text_requests_today"] + 1
            image_requests = user_data["image_requests_today"]

            await update_user_limits(
                user_data["id"],
                text_requests,
                image_requests,
                current_moscow_time.isoformat()
            )

    except Exception as e:
        logger.error(f"Неожиданная ошибка в handle_voice_message: {e}", exc_info=True)
        try:
            await message.answer("Произошла неожиданная ошибка. Пожалуйста, попробуйте еще раз позже.")
        except Exception as inner_e:
            logger.error(f"Ошибка при отправке сообщения об ошибке пользователю: {inner_e}")

        if processing_message:
            try:
                await processing_message.delete()
            except Exception as del_e:
                logger.error(f"Ошибка при удалении processing_message в блоке except: {del_e}")